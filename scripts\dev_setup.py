#!/usr/bin/env python3
"""
Development setup script for 171 Bureau De Change
Sets up the development environment
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description, cwd=None):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            cwd=cwd,
            capture_output=True,
            text=True
        )
        print(f"✓ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_node_version():
    """Check if Node.js is installed"""
    print("📦 Checking Node.js version...")
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        print(f"✓ Node.js {version} is installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Node.js is not installed or not in PATH")
        print("Please install Node.js from https://nodejs.org/")
        return False

def setup_python_environment():
    """Set up Python virtual environment and install dependencies"""
    print("🐍 Setting up Python environment...")
    
    # Create virtual environment if it doesn't exist
    if not os.path.exists("venv"):
        if not run_command("python -m venv venv", "Creating virtual environment"):
            return False
    
    # Determine activation script based on OS
    if platform.system() == "Windows":
        activate_script = "venv\\Scripts\\activate"
        pip_command = "venv\\Scripts\\pip"
    else:
        activate_script = "venv/bin/activate"
        pip_command = "venv/bin/pip"
    
    # Install Python dependencies
    if not run_command(f"{pip_command} install -r requirements.txt", "Installing Python dependencies"):
        return False
    
    print(f"✓ Python environment setup completed")
    print(f"  To activate: {activate_script}")
    return True

def setup_node_environment():
    """Set up Node.js environment and install dependencies"""
    print("📦 Setting up Node.js environment...")
    
    # Install Node.js dependencies
    if not run_command("npm install", "Installing Node.js dependencies"):
        return False
    
    print("✓ Node.js environment setup completed")
    return True

def setup_database():
    """Initialize the database"""
    print("🗄️ Setting up database...")
    
    # Run database initialization script
    if platform.system() == "Windows":
        python_command = "venv\\Scripts\\python"
    else:
        python_command = "venv/bin/python"
    
    if not run_command(f"{python_command} scripts/init_db.py", "Initializing database"):
        return False
    
    return True

def create_env_file():
    """Create .env file if it doesn't exist"""
    print("⚙️ Setting up environment configuration...")
    
    if os.path.exists(".env"):
        print("✓ .env file already exists")
        return True
    
    # Copy from .env.example
    if os.path.exists(".env.example"):
        try:
            with open(".env.example", "r") as src, open(".env", "w") as dst:
                dst.write(src.read())
            print("✓ .env file created from .env.example")
            return True
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    else:
        print("❌ .env.example file not found")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up 171 Bureau De Change Development Environment")
    print("=" * 60)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        sys.exit(1)
    
    # Setup steps
    steps = [
        ("Environment configuration", create_env_file),
        ("Python environment", setup_python_environment),
        ("Node.js environment", setup_node_environment),
        ("Database", setup_database),
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        print(f"\n📋 Setting up {step_name}...")
        if not step_function():
            failed_steps.append(step_name)
    
    print("\n" + "=" * 60)
    
    if failed_steps:
        print("❌ Setup completed with errors:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nPlease fix the errors and run the setup again.")
        sys.exit(1)
    else:
        print("✅ Development environment setup completed successfully!")
        print("\n🚀 To start the application:")
        print("1. Backend: uvicorn app.main:app --reload")
        print("2. Frontend: npm start")
        print("3. Open http://localhost:3000 in your browser")
        print("4. Login with admin/admin123")

if __name__ == "__main__":
    main()
